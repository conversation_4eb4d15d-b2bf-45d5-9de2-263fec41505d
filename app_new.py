import os
import streamlit as st
from dotenv import load_dotenv
from openai import AzureOpenAI
import ell
from pathlib import Path
import docx2txt
import PyPDF2
import pandas as pd
import io
from pptx import Presentation
from PIL import Image as PILImage
import tempfile
import xml.etree.ElementTree as ET

# Load environment variables from .env
load_dotenv()

api_key = os.getenv("API_KEY")
model = os.getenv("MODEL", "gpt-4o")
azure_endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")

st.title("Document Review Assistant - Azure OpenAI (Volvo)")

st.sidebar.header("Model Settings")
st.sidebar.write(f"Model: **{model}**")
st.sidebar.write(f"Azure Endpoint: {azure_endpoint}")

st.write("This app uses Azure OpenAI to compare documents against requirements and provides detailed feedback.")

with st.expander("How this app works"):
    st.markdown("""
    1. **Input Requirements**: Provide a folder containing documents that define the requirements/criteria.
    2. **Input Documents to Review**: Provide a folder containing the documents that need to be reviewed.
    3. **LLM Analysis**: The app extracts text from all documents and uses the LLM to compare them.
    4. **Results**: The LLM provides a detailed analysis of what requirements are met and what's missing.
    """)

with st.expander("AI Questions"):
    st.markdown("""
    **How does the AI process documents and answer questions?**
    - Extracts and understands document content.
    - Compares content with requirements.
    - Answers questions with evidence-based responses.
    """)

def extract_text_from_file(file_path):
    ext = file_path.suffix.lower()
    try:
        if ext == ".txt":
            with open(file_path, "r", encoding="utf-8") as f:
                return f.read()
        elif ext == ".docx":
            return docx2txt.process(file_path)
        elif ext == ".pdf":
            text = ""
            with open(file_path, "rb") as f:
                reader = PyPDF2.PdfReader(f)
                for page in reader.pages:
                    text += page.extract_text() or ""
            return text
        elif ext == ".pptx":
            text = ""
            presentation = Presentation(file_path)
            for slide in presentation.slides:
                for shape in slide.shapes:
                    if hasattr(shape, "text"):
                        text += shape.text + "\n"
            return text
        elif ext == ".xlsx":
            excel_data = pd.read_excel(file_path, sheet_name=None, engine="openpyxl")
            text = ""
            for sheet, df in excel_data.items():
                text += df.to_string(index=False) + "\n"
            return text
        elif ext == ".drawio":
            tree = ET.parse(file_path)
            root = tree.getroot()
            text_elements = []
            for element in root.iter():
                if element.tag == "mxCell" and "value" in element.attrib:
                    value = element.attrib["value"]
                    if value.strip():
                        text_elements.append(value)
            return "\n".join(text_elements)
        else:
            return f"Unsupported file format: {ext}"
    except Exception as e:
        return f"Error processing {file_path.name}: {e}"

def get_files_from_directory(directory_path):
    if not directory_path:
        return []
    supported = [".txt", ".docx", ".pdf", ".pptx", ".xlsx", ".drawio"]
    files = []
    for file_path in Path(directory_path).glob("**/*"):
        if file_path.is_file() and file_path.suffix.lower() in supported:
            files.append(file_path)
    return files

# Input for requirements folder
st.subheader("Step 1: Select Requirements Folder")
requirements_folder = st.text_input("Enter the path to the folder containing requirement documents:")
st.caption("Supported formats: .txt, .docx, .pdf, .pptx, .xlsx, .drawio")

# Input for review documents folder
st.subheader("Step 2: Select Documents to Review")
review_folder = st.text_input("Enter the path to the folder containing documents to be reviewed:")
st.caption("Supported formats: .txt, .docx, .pdf, .pptx, .xlsx, .drawio")

if st.button("Analyze Documents"):
    if not requirements_folder or not review_folder:
        st.error("Please provide both folder paths.")
    elif not api_key or not azure_endpoint:
        st.error("API_KEY or AZURE_OPENAI_ENDPOINT is missing in .env file.")
    else:
        requirements_files = get_files_from_directory(requirements_folder)
        review_files = get_files_from_directory(review_folder)
        if not requirements_files:
            st.error("No supported files found in requirements folder.")
        elif not review_files:
            st.error("No supported files found in review folder.")
        else:
            # Progress bar and file processing display
            total_files = len(requirements_files) + len(review_files)
            progress_bar = st.progress(0)
            
            requirements_text = ""
            st.subheader("Requirements Documents")
            for i, file_path in enumerate(requirements_files):
                progress_bar.progress((i / total_files) * 0.5)
                file_text = extract_text_from_file(file_path)
                requirements_text += f"\n\n--- {file_path.name} ---\n{file_text}"
                st.write(f"✓ Processed requirement file: **{file_path.name}**")
            
            review_text = ""
            st.subheader("Documents to Review")
            for i, file_path in enumerate(review_files):
                progress_bar.progress(0.5 + (i / len(review_files)) * 0.3)
                file_text = extract_text_from_file(file_path)
                review_text += f"\n\n--- {file_path.name} ---\n{file_text}"
                st.write(f"✓ Processed review file: **{file_path.name}**")

            st.subheader("LLM Analysis Process")
            st.info(f"Using LLM model **{model}** to analyze documents...")
            st.write("The LLM is now comparing the documents against the requirements criteria.")
            st.write("This process involves:")
            st.markdown("""
            1. **Extracting key requirements** from the requirements documents
            2. **Identifying evidence** in the review documents
            3. **Evaluating compliance** for each requirement
            4. **Generating recommendations** for missing or incomplete items
            """)
            progress_bar.progress(0.8)

            # Use ell if available, otherwise use AzureOpenAI directly
            try:
                @ell.simple(model=model, temperature=0.2, client=AzureOpenAI(
                    api_key=api_key,
                    azure_endpoint=azure_endpoint,
                    api_version="2024-06-01"
                ))
                def analyze_documents(requirements_text, review_text):
                    return [
                        ell.system("You are a document review assistant that compares documents against requirements and architecture principles. Identify what is met and what is missing."),
                        ell.user(f"""
                        REQUIREMENTS:
                        {requirements_text}

                        DOCUMENTS TO REVIEW:
                        {review_text}

                        Please analyze if the documents meet all requirements and North Star Architecture Principles. For each:
                        1. State if it is met or not
                        2. Provide specific evidence from the documents
                        3. If a requirement or principle is not met, explain what is missing or needs to be added
                        4. Explicitly include a section for the North Star Architecture Principles, stating if each principle is met or not.

                        Format your response with headings and bullet points.
                        """)
                    ]
                result = analyze_documents(requirements_text, review_text)
                progress_bar.progress(1.0)
                st.subheader("Analysis Results")
                st.markdown(result)
            except Exception as e:
                # Fallback to direct AzureOpenAI call
                try:
                    client = AzureOpenAI(
                        api_key=api_key,
                        azure_endpoint=azure_endpoint,
                        api_version="2024-06-01"
                    )
                    response = client.chat.completions.create(
                        model=model,
                        messages=[
                            {"role": "system", "content": "You are a document review assistant that compares documents against requirements and architecture principles. Identify what is met and what is missing."},
                            {"role": "user", "content": f"""
REQUIREMENTS:
{requirements_text}

DOCUMENTS TO REVIEW:
{review_text}

Please analyze if the documents meet all requirements and North Star Architecture Principles. For each:
1. State if it is met or not
2. Provide specific evidence from the documents
3. If a requirement or principle is not met, explain what is missing or needs to be added
4. Explicitly include a section for the North Star Architecture Principles, stating if each principle is met or not.

Format your response with headings and bullet points.
"""}
                        ],
                        temperature=0.2
                    )
                    progress_bar.progress(1.0)
                    st.subheader("Analysis Results")
                    st.markdown(response.choices[0].message.content)
                except Exception as e2:
                    st.error(f"Error: {e2}")
