import os
import streamlit as st
from dotenv import load_dotenv
from openai import AzureOpenAI

# Load environment variables from .env
load_dotenv()

# Streamlit UI
st.title("Azure OpenAI Chatbot (Volvo)")
st.write("Interact with your Azure OpenAI model using Streamlit.")

api_key = os.getenv("API_KEY")
model = os.getenv("MODEL", "gpt-4o")
azure_endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")

# Input area for user prompt
def get_user_input():
    return st.text_area("Your message:", "Hej, vilken modell är du?")

user_input = get_user_input()

if st.button("Send"):
    if not api_key or not azure_endpoint:
        st.error("API_KEY or AZURE_OPENAI_ENDPOINT is missing in .env file.")
    elif not user_input.strip():
        st.warning("Please enter a message.")
    else:
        try:
            client = AzureOpenAI(
                api_key=api_key,
                azure_endpoint=azure_endpoint,
                api_version="2024-06-01"
            )
            response = client.chat.completions.create(
                model=model,
                messages=[{"role": "user", "content": user_input}],
                temperature=0.7
            )
            st.success("Response:")
            st.write(response.choices[0].message.content)
        except Exception as e:
            st.error(f"Error: {e}")
