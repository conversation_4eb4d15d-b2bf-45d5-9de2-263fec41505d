import os
import streamlit as st
from dotenv import load_dotenv
from openai import AzureOpenAI
import asyncio, warnings, copy, time

# PocketFlow implementation (100 lines)
class BaseNode:
    def __init__(self):
        self.params,self.successors={},{}
    def set_params(self,params):
        self.params=params
    def next(self,node,action="default"):
        if action in self.successors: warnings.warn(f"Overwriting successor for action '{action}'")
        self.successors[action]=node; return node
    def prep(self,shared):
        pass
    def exec(self,prep_res):
        pass
    def post(self,shared,prep_res,exec_res):
        pass
    def _exec(self,prep_res):
        return self.exec(prep_res)
    def _run(self,shared):
        p=self.prep(shared); e=self._exec(p); return self.post(shared,p,e)
    def run(self,shared):
        if self.successors: warnings.warn("Node won't run successors. Use Flow.")
        return self._run(shared)
    def __rshift__(self,other):
        return self.next(other)
    def __sub__(self,action):
        if isinstance(action,str): return _ConditionalTransition(self,action)
        raise TypeError("Action must be a string")

class _ConditionalTransition:
    def __init__(self,src,action):
        self.src,self.action=src,action
    def __rshift__(self,tgt):
        return self.src.next(tgt,self.action)

class Node(BaseNode):
    def __init__(self,max_retries=1,wait=0):
        super().__init__(); self.max_retries,self.wait=max_retries,wait
    def exec_fallback(self,prep_res,exc):
        raise exc
    def _exec(self,prep_res):
        for self.cur_retry in range(self.max_retries):
            try: return self.exec(prep_res)
            except Exception as e:
                if self.cur_retry==self.max_retries-1: return self.exec_fallback(prep_res,e)
                if self.wait>0: time.sleep(self.wait)

class Flow(BaseNode):
    def __init__(self,start=None):
        super().__init__(); self.start_node=start
    def start(self,start):
        self.start_node=start; return start
    def get_next_node(self,curr,action):
        nxt=curr.successors.get(action or "default")
        if not nxt and curr.successors: warnings.warn(f"Flow ends: '{action}' not found in {list(curr.successors)}")
        return nxt
    def _orch(self,shared,params=None):
        curr,p,last_action =copy.copy(self.start_node),(params or {**self.params}),None
        while curr: curr.set_params(p); last_action=curr._run(shared); curr=copy.copy(self.get_next_node(curr,last_action))
        return last_action
    def _run(self,shared):
        p=self.prep(shared); o=self._orch(shared); return self.post(shared,p,o)
    def post(self,shared,prep_res,exec_res):
        return exec_res

# Load environment variables from .env
load_dotenv()

# Streamlit UI
st.title("Azure OpenAI Chatbot with PocketFlow (Volvo)")
st.write("Interact with your Azure OpenAI model using Streamlit and PocketFlow framework.")

# Get environment variables
api_key = os.getenv("API_KEY")
model = os.getenv("MODEL", "gpt-4o")
azure_endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")

# Initialize session state for messages
if "messages" not in st.session_state:
    st.session_state.messages = []

# Display chat history
for message in st.session_state.messages:
    with st.chat_message(message["role"]):
        st.markdown(message["content"])

# PocketFlow-based Chat Implementation

# Create a simpler PocketFlow implementation for this use case
class ChatFlow(Node):
    """Simplified flow that handles the entire chat process"""
    def prep(self, shared):
        return shared.get("user_input", "")

    def exec(self, user_input):
        if not user_input.strip():
            return None

        # Add user message to session state
        st.session_state.messages.append({"role": "user", "content": user_input})

        # Prepare messages for LLM
        messages = [{"role": msg["role"], "content": msg["content"]}
                   for msg in st.session_state.messages]

        try:
            # Call Azure OpenAI
            client = AzureOpenAI(
                api_key=api_key,
                azure_endpoint=azure_endpoint,
                api_version="2024-06-01"
            )
            response = client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=0.7
            )
            assistant_response = response.choices[0].message.content

            # Add assistant message to session state
            st.session_state.messages.append({"role": "assistant", "content": assistant_response})

            return assistant_response

        except Exception as e:
            error_msg = f"Error: {e}"
            st.session_state.messages.append({"role": "assistant", "content": error_msg})
            return error_msg

    def post(self, shared, prep_res, exec_res):
        _ = shared, prep_res  # Suppress unused parameter warnings
        # Display the assistant's response
        if exec_res:
            with st.chat_message("assistant"):
                st.markdown(exec_res)
        return None

# Create the chat flow
chat_flow = ChatFlow()

# Chat input
if prompt := st.chat_input("Your message:"):
    # Display user message
    with st.chat_message("user"):
        st.markdown(prompt)

    # Check for missing configuration
    if not api_key or not azure_endpoint:
        st.error("API_KEY or AZURE_OPENAI_ENDPOINT is missing in .env file.")
    else:
        # Run the PocketFlow
        shared = {"user_input": prompt}

        with st.spinner("Processing..."):
            try:
                chat_flow.run(shared)
            except Exception as e:
                st.error(f"Error processing request: {e}")

# Sidebar with information
with st.sidebar:
    # st.header("PocketFlow Configuration")
    st.write(f"**Model:** {model}")
    # st.write(f"**Endpoint:** {azure_endpoint}")
    
    if st.button("Clear Chat History"):
        st.session_state.messages = []
        st.rerun()
    
    # st.markdown("---")
    # st.markdown("### About PocketFlow")
    # st.markdown("""
    # This chatbot uses **PocketFlow**, a 100-line minimalist LLM framework that provides:
    # - **Node-based architecture** for modular processing
    # - **Flow orchestration** for complex workflows
    # - **Lightweight design** with zero dependencies

    # The chat flow uses a single **ChatFlow** node that:
    # 1. **Processes user input** and adds to conversation history
    # 2. **Calls Azure OpenAI** with the full conversation context
    # 3. **Displays the response** and updates the chat interface

    # This demonstrates PocketFlow's flexibility - you can use simple single-node flows
    # or complex multi-node workflows depending on your needs.
    # """)
