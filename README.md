# Document Review Assistant

This application uses a Large Language Model (LLM) to compare documents against requirements criteria and provides detailed feedback on what is missing or needs to be added.

## Features

- Load requirements documents from a specified folder
- Load documents to be reviewed from another folder
- Extract text from various document formats (TXT, DOCX, PDF, PPTX, XLSX)
- Extract text from images in PowerPoint presentations using gemma3:27b's multimodal capabilities
- Use an LLM with the `@ell.simple` decorator to analyze if the documents meet the requirements
- Generate a detailed report with findings and recommendations
- Download the analysis results
- Select from multiple LLM models in the sidebar

## Setup

1. Install the required dependencies:
   ```
   pip install -r requirements.txt
   ```

2. Create a `.env` file with your Ollama URL:
   ```
   OLLAMA_URL=http://localhost:11434/v1
   ```

3. Run the application:
   ```
   streamlit run app.py
   ```

## How to Use

1. Select an LLM model from the sidebar:
   - gemma3:27b (recommended - includes multimodal capabilities for extracting text from images)
   - llama3.2:70b
   - llama3.1:8b
2. Enter the path to the folder containing requirement documents
3. Enter the path to the folder containing documents to be reviewed
4. Click "Analyze Documents"
5. Review the analysis results
6. Download the analysis if needed

## Supported File Formats

- Text files (.txt)
- Word documents (.docx)
- PDF files (.pdf)
- PowerPoint presentations (.pptx) - with image text extraction when using gemma3:27b
- Excel spreadsheets (.xlsx)

## Requirements

- Python 3.6+
- Streamlit
- docx2txt
- PyPDF2
- OpenAI API client
- ell package
- python-pptx
- pandas and openpyxl
- Pillow (for image processing)
- Ollama (running locally or remotely)
