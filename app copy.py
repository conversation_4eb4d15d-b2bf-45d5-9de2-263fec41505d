import streamlit as st
import os
from pathlib import Path
import docx2txt
import PyPDF2
import re
from openai import OpenAI
import ell
from dotenv import load_dotenv
from pptx import Presentation
import pandas as pd
import io
from PIL import Image as PILImage
from ell.types.message import ImageContent
import tempfile

# Load environment variables from the .env file
load_dotenv()

# Initialize the OpenAI client
client = OpenAI(
    base_url=os.getenv("OLLAMA_URL"),
    api_key="ollama",
)

# Default model name
default_model = "gemma3:27b"

# Streamlit app starts here
st.title("Document Review Assistant - Powered by LLM")

# Model selection
st.sidebar.header("Model Settings")
model_options = ["gemma3:27b", "llama3.2:70b", "llama3.1:8b"]
selected_model = st.sidebar.selectbox("Select LLM Model", model_options, index=0)
model = selected_model

# Add note about vision capabilities
st.sidebar.info("✨ Note: gemma3:27b has multimodal capabilities and will extract text from images in PowerPoint presentations.")

st.write(f"This app uses the **{model}** LLM model to compare documents against requirements criteria and provides detailed feedback.")

# Explanation of how the app works
with st.expander("How this app works"):
    st.markdown("""
    **This app uses an LLM model to review documents against requirements:**
    
    1. **Input Requirements**: Provide a folder containing documents that define the requirements/criteria
    2. **Input Documents to Review**: Provide a folder containing the documents that need to be reviewed
    3. **LLM Analysis**: The app extracts text from all documents and uses the LLM to compare them
    4. **Results**: The LLM provides a detailed analysis of what requirements are met and what's missing
    
    The LLM model acts as an expert reviewer, carefully reading both sets of documents and providing insights that would be difficult to automate with traditional rule-based approaches.
    """)

@ell.simple(model="gemma3:27b", temperature=0.2, client=client)
def extract_text_from_image(image):
    """Extract text from image using LLM vision capabilities"""
    return [
        ell.system("You are a helpful assistant that extracts text from images. Describe all visible text in the image in detail."),
        ell.user(["Please extract and transcribe all text visible in this image. Include any important information, tables, or diagrams.", image])
    ]

def extract_images_from_pptx(file_path):
    """Extract images from PowerPoint presentation"""
    presentation = Presentation(file_path)
    images = []
    
    for slide_num, slide in enumerate(presentation.slides):
        for shape in slide.shapes:
            if hasattr(shape, "image"):
                try:
                    # Get image data
                    image_bytes = shape.image.blob
                    # Convert to PIL Image
                    img = PILImage.open(io.BytesIO(image_bytes))
                    # Add slide number information
                    images.append((slide_num + 1, img))
                except Exception as e:
                    print(f"Error extracting image from slide {slide_num + 1}: {str(e)}")
    
    return images

def extract_text_from_file(file_path):
    """Extract text from various file formats"""
    file_extension = file_path.suffix.lower()
    
    try:
        if file_extension == '.txt':
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read()
        
        elif file_extension == '.docx':
            return docx2txt.process(file_path)
        
        elif file_extension == '.pdf':
            text = ""
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page_num in range(len(pdf_reader.pages)):
                    text += pdf_reader.pages[page_num].extract_text()
            return text
        
        elif file_extension == '.pptx':
            text = ""
            # Extract text from shapes
            presentation = Presentation(file_path)
            for slide_num, slide in enumerate(presentation.slides):
                text += f"\n--- Slide {slide_num + 1} ---\n"
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text:
                        text += shape.text + "\n"
            
            # Extract text from images (gemma3:27b has multimodal capabilities)
            images = extract_images_from_pptx(file_path)
            if images:
                text += "\n--- Text Extracted from Images ---\n"
                for slide_num, img in images:
                    try:
                        # Save image to temporary file to ensure it's in a format the LLM can process
                        with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as temp_file:
                            img.save(temp_file, format="PNG")
                            temp_file_path = temp_file.name
                        
                        # Open the saved image
                        processed_img = PILImage.open(temp_file_path)
                        
                        # Extract text from image using LLM
                        image_text = extract_text_from_image(processed_img)
                        text += f"\n--- Image from Slide {slide_num} ---\n{image_text}\n"
                        
                        # Clean up temporary file
                        os.unlink(temp_file_path)
                    except Exception as e:
                        text += f"\nError processing image from slide {slide_num}: {str(e)}\n"
            
            return text
        
        elif file_extension == '.xlsx':
            text = ""
            try:
                # Read all sheets with explicit engine specification
                excel_data = pd.read_excel(file_path, sheet_name=None, engine='openpyxl')
                
                # Process each sheet
                for sheet_name, df in excel_data.items():
                    text += f"\n--- Sheet: {sheet_name} ---\n"
                    
                    # Convert dataframe to string representation
                    text += df.to_string(index=False) + "\n"
                
                return text
            except ImportError as e:
                return f"Error: openpyxl is required for Excel files. Please install it with 'pip install openpyxl'. Details: {str(e)}"
            except Exception as e:
                return f"Error processing Excel file {file_path.name}: {str(e)}"
        
        else:
            return f"Unsupported file format: {file_extension}"
    
    except Exception as e:
        return f"Error processing file {file_path.name}: {str(e)}"

def get_files_from_directory(directory_path):
    """Get all document files from a directory"""
    if not directory_path:
        return []
    
    supported_extensions = ['.txt', '.docx', '.pdf', '.pptx', '.xlsx']
    files = []
    
    try:
        for file_path in Path(directory_path).glob('**/*'):
            if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                files.append(file_path)
    except Exception as e:
        st.error(f"Error accessing directory: {str(e)}")
    
    return files

@ell.simple(model=default_model, temperature=0.2, client=client)
def analyze_documents(requirements_text, review_documents_text, model_name=None):
    """Analyze if review documents meet the requirements using the LLM model"""
    # Update the model if a specific one is provided
    if model_name and model_name != default_model:
        analyze_documents.model = model_name
        
    return [
        ell.system("You are a document review assistant that carefully compares documents against requirements criteria. Your job is to identify what meets the criteria and what is missing or needs to be added."),
        ell.user(f"""
        Your task is to analyze if the provided documents meet the specified requirements.
        
        REQUIREMENTS:
        {requirements_text}
        
        DOCUMENTS TO REVIEW:
        {review_documents_text}
        
        Please analyze if the documents meet all the requirements. For each requirement:
        1. State if it is met or not
        2. Provide specific evidence from the documents
        3. If a requirement is not met, explain what is missing or needs to be added
        
        Format your response in a clear, structured way with headings and bullet points.
        """)
    ]

# Input for requirements folder
st.subheader("Step 1: Select Requirements Folder")
requirements_folder = st.text_input("Enter the path to the folder containing requirement documents:")
st.caption("This folder should contain documents that define the criteria for passing the review.")
st.caption("Supported formats: .txt, .docx, .pdf, .pptx, .xlsx" + (" (with image text extraction)" if model == "gemma3:27b" else ""))

# Input for review documents folder
st.subheader("Step 2: Select Documents to Review")
review_folder = st.text_input("Enter the path to the folder containing documents to be reviewed:")
st.caption("This folder should contain the documents that need to be checked against the requirements.")
st.caption("Supported formats: .txt, .docx, .pdf, .pptx, .xlsx" + (" (with image text extraction)" if model == "gemma3:27b" else ""))

if st.button("Analyze Documents"):
    if not requirements_folder or not review_folder:
        st.error("Please provide both folder paths.")
    else:
        with st.spinner("Processing documents..."):
            # Get files from both directories
            requirements_files = get_files_from_directory(requirements_folder)
            review_files = get_files_from_directory(review_folder)
            
            if not requirements_files:
                st.error(f"No supported document files found in the requirements folder: {requirements_folder}")
            elif not review_files:
                st.error(f"No supported document files found in the review folder: {review_folder}")
            else:
                # Display found files
                st.write(f"Found {len(requirements_files)} requirement files and {len(review_files)} files to review.")
                
                # Progress bar for document processing
                progress_bar = st.progress(0)
                
                # Extract text from requirements files
                st.subheader("Requirements Documents")
                requirements_text = ""
                for i, file_path in enumerate(requirements_files):
                    progress_bar.progress((i / (len(requirements_files) + len(review_files))) * 0.5)
                    file_text = extract_text_from_file(file_path)
                    requirements_text += f"\n\n--- {file_path.name} ---\n{file_text}"
                    st.write(f"✓ Processed requirement file: **{file_path.name}**")
                
                # Extract text from review files
                st.subheader("Documents to Review")
                review_documents_text = ""
                for i, file_path in enumerate(review_files):
                    progress_bar.progress(0.5 + (i / len(review_files)) * 0.3)
                    file_text = extract_text_from_file(file_path)
                    review_documents_text += f"\n\n--- {file_path.name} ---\n{file_text}"
                    st.write(f"✓ Processed review file: **{file_path.name}**")
                
                # LLM Analysis section
                st.subheader("LLM Analysis Process")
                st.info(f"Using LLM model **{model}** to analyze documents...")
                st.write("The LLM is now comparing the documents against the requirements criteria.")
                st.write("This process involves:")
                st.markdown("""
                1. **Extracting key requirements** from the requirements documents
                2. **Identifying evidence** in the review documents
                3. **Evaluating compliance** for each requirement
                4. **Generating recommendations** for missing or incomplete items
                """)
                
                progress_bar.progress(0.8)
                
                # Analyze documents
                analysis_result = analyze_documents(requirements_text, review_documents_text, model_name=model)
                progress_bar.progress(1.0)
                
                # Display results
                st.subheader("Analysis Results")
                st.markdown(analysis_result)
                
                # Option to download the analysis
                st.download_button(
                    label="Download Analysis",
                    data=analysis_result,
                    file_name="document_review_analysis.txt",
                    mime="text/plain"
                )
